import { GdprCheckResult, Evidence, Recommendation } from '../types';
import {
  EnhancedCheckTemplate,
  EnhancedCheckConfig,
  CheckExecutionContext,
  CheckResult,
} from '../utils/enhanced-check-template';

export interface PrivacyPolicyCheckConfig {
  targetUrl: string;
  timeout: number;
  scanId?: string;
}

export class PrivacyPolicyCheck {
  private checkTemplate: EnhancedCheckTemplate;

  constructor() {
    this.checkTemplate = new EnhancedCheckTemplate();
  }

  /**
   * Check for privacy policy presence and accessibility
   * ENHANCED IMPLEMENTATION - uses smart URL resolution and content discovery
   */
  async performCheck(config: PrivacyPolicyCheckConfig): Promise<GdprCheckResult> {
    const enhancedConfig: EnhancedCheckConfig = {
      targetUrl: config.targetUrl,
      timeout: config.timeout,
      scanId: config.scanId,
      retryAttempts: 3,
      enableJavaScript: true,
      enableImages: false, // Not needed for privacy policy detection
      followRedirects: true,
    };

    return this.checkTemplate.executeCheck(
      'GDPR-002',
      'Privacy Policy Presence',
      'privacy_policy',
      7,
      'high',
      enhancedConfig,
      this.executePrivacyPolicyCheck.bind(this),
      true, // Requires browser
      false // No manual review required
    );
  }

  /**
   * Execute the privacy policy check logic
   */
  private async executePrivacyPolicyCheck(context: CheckExecutionContext): Promise<CheckResult> {
    const startTime = Date.now();
    const evidence: Evidence[] = [];
    let score = 0;
    let passed = false;

    try {
      // Step 1: Use enhanced content discovery to find privacy policy links
      const privacyLinks = EnhancedCheckTemplate.findPrivacyPolicyLinks(context);

      if (privacyLinks.length === 0) {
        evidence.push({
          type: 'text',
          description: 'No privacy policy links found',
          value: 'No links matching privacy policy patterns were detected on the page',
        });
        score = 0;
        passed = false;
      } else {
        // Step 2: Analyze found privacy policy links
        const bestLink = privacyLinks[0]; // Highest confidence link

        evidence.push({
          type: 'link',
          description: 'Privacy policy link found',
          value: bestLink.url,
          location: 'Main page',
        });

        // Step 3: Test accessibility of the privacy policy link
        const linkAccessible = await this.testPrivacyPolicyAccessibility(context, bestLink.url);

        if (linkAccessible.accessible) {
          evidence.push({
            type: 'text',
            description: 'Privacy policy page is accessible',
            value: `Status: ${linkAccessible.statusCode}, Content length: ${linkAccessible.contentLength} characters`,
          });
          score += 60; // Base score for accessible privacy policy

          // Step 4: Analyze privacy policy content quality
          if (linkAccessible.content) {
            const contentAnalysis = this.analyzePrivacyPolicyContent(linkAccessible.content);
            evidence.push(...contentAnalysis.evidence);
            score += contentAnalysis.score;
          }
        } else {
          evidence.push({
            type: 'text',
            description: 'Privacy policy link is not accessible',
            value: linkAccessible.error || 'Failed to access privacy policy page',
          });
          score = 20; // Partial score for having a link but not accessible
        }

        // Step 5: Check for multiple privacy policy links (good practice)
        if (privacyLinks.length > 1) {
          evidence.push({
            type: 'text',
            description: 'Multiple privacy policy links found',
            value: `Found ${privacyLinks.length} privacy policy links, improving accessibility`,
          });
          score += 10; // Bonus for multiple links
        }

        // Step 6: Check link placement and visibility
        const linkPlacement = this.analyzeLinkPlacement(context, privacyLinks);
        evidence.push(...linkPlacement.evidence);
        score += linkPlacement.score;

        passed = score >= 70;
      }

      // Ensure score is within bounds
      score = Math.max(0, Math.min(100, score));

      return {
        passed,
        score,
        evidence,
        recommendations: this.generateRecommendations(privacyLinks, score),
        executionTime: Date.now() - startTime,
      };
    } catch (error) {
      return {
        passed: false,
        score: 0,
        evidence: [
          {
            type: 'text',
            description: 'Privacy policy check failed',
            value: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendations: [
          {
            priority: 1,
            title: 'Fix website accessibility for privacy policy scanning',
            description: 'Ensure the website is accessible and privacy policy links are properly formatted',
            implementation: 'Check website loading, SSL certificates, and HTML structure',
            effort: 'moderate' as const,
            impact: 'high' as const,
          },
        ],
        executionTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Test accessibility of privacy policy link
   */
  private async testPrivacyPolicyAccessibility(
    context: CheckExecutionContext,
    privacyPolicyUrl: string
  ): Promise<{
    accessible: boolean;
    statusCode: number;
    contentLength: number;
    content?: string;
    error?: string;
  }> {
    try {
      // Use URL resolver to test accessibility
      const resolved = await context.urlResolver.resolveUrl(privacyPolicyUrl, {
        timeout: 15000,
        retryAttempts: 2,
      });

      if (resolved.isAccessible) {
        // Try to get content if accessible
        let content = '';
        try {
          if (context.page) {
            await context.page.goto(privacyPolicyUrl, {
              waitUntil: 'domcontentloaded',
              timeout: 15000
            });
            content = await context.page.$eval('body', el => el.textContent || '') || '';
          }
        } catch (error) {
          console.warn('Failed to get privacy policy content:', error);
        }

        return {
          accessible: true,
          statusCode: resolved.statusCode,
          contentLength: content.length,
          content,
        };
      } else {
        return {
          accessible: false,
          statusCode: resolved.statusCode,
          contentLength: 0,
          error: resolved.error,
        };
      }
    } catch (error) {
      return {
        accessible: false,
        statusCode: 0,
        contentLength: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Analyze privacy policy content for GDPR compliance
   */
  private analyzePrivacyPolicyContent(content: string): { score: number; evidence: Evidence[] } {
    const patterns = [
      { name: 'Data collection purpose', regex: /purpose.*collect|collect.*purpose|why.*collect/i, weight: 15 },
      { name: 'Legal basis for processing', regex: /legal basis|lawful basis|legitimate interest/i, weight: 15 },
      { name: 'Data subject rights', regex: /your rights|data subject|right to access|right to rectification|right to erasure/i, weight: 20 },
      { name: 'Data retention period', regex: /retention|how long|period.*store|store.*period/i, weight: 10 },
      { name: 'Third party sharing', regex: /third party|share.*data|data.*share|partners/i, weight: 10 },
      { name: 'Contact information', regex: /contact.*us|data protection officer|dpo|privacy.*officer/i, weight: 10 },
      { name: 'Cookie information', regex: /cookie|tracking|analytics/i, weight: 10 },
      { name: 'Data transfers', regex: /transfer.*data|international.*transfer|adequacy/i, weight: 10 },
    ];

    return EnhancedCheckTemplate.analyzeTextContent(content, patterns);
  }

  /**
   * Analyze privacy policy link placement and visibility
   */
  private analyzeLinkPlacement(
    context: CheckExecutionContext,
    privacyLinks: Array<{ url: string; confidence: number }>
  ): { score: number; evidence: Evidence[] } {
    const evidence: Evidence[] = [];
    let score = 0;

    // Check if privacy policy is in content discovery results
    if (context.contentDiscovery) {
      const discoveredLinks = context.contentDiscovery.privacyPolicyLinks;

      // Check link locations
      const locations = discoveredLinks.map(link => link.location);
      const hasFooterLink = locations.includes('footer');
      const hasHeaderLink = locations.includes('header') || locations.includes('nav');

      if (hasFooterLink) {
        evidence.push({
          type: 'text',
          description: 'Privacy policy link found in footer',
          value: 'Good practice - footer placement improves accessibility',
        });
        score += 5;
      }

      if (hasHeaderLink) {
        evidence.push({
          type: 'text',
          description: 'Privacy policy link found in header/navigation',
          value: 'Excellent practice - header placement ensures high visibility',
        });
        score += 10;
      }

      // Check for high-confidence links
      const highConfidenceLinks = discoveredLinks.filter(link => link.confidence > 0.8);
      if (highConfidenceLinks.length > 0) {
        evidence.push({
          type: 'text',
          description: 'High-confidence privacy policy links found',
          value: `${highConfidenceLinks.length} links with clear privacy policy labeling`,
        });
        score += 5;
      }
    }

    return { score, evidence };
  }

  /**
   * Generate recommendations based on check results
   */
  private generateRecommendations(
    privacyLinks: Array<{ url: string; confidence: number }>,
    score: number
  ): Recommendation[] {
    const recommendations: Recommendation[] = [];

    if (privacyLinks.length === 0) {
      recommendations.push({
        priority: 1,
        title: 'Add privacy policy link',
        description: 'Create and link to a comprehensive privacy policy',
        implementation: 'Add a clearly labeled "Privacy Policy" link in the footer and/or header',
        effort: 'moderate' as const,
        impact: 'high' as const,
      });
    } else if (score < 70) {
      recommendations.push({
        priority: 1,
        title: 'Improve privacy policy accessibility',
        description: 'Ensure privacy policy is easily accessible and comprehensive',
        implementation: 'Fix broken links, improve content, and ensure proper placement',
        effort: 'moderate' as const,
        impact: 'high' as const,
      });
    }

    if (score < 50) {
      recommendations.push({
        priority: 2,
        title: 'Enhance privacy policy content',
        description: 'Include all required GDPR elements in privacy policy',
        implementation: 'Add sections on legal basis, data subject rights, retention periods, and contact information',
        effort: 'significant' as const,
        impact: 'high' as const,
      });
    }

    if (privacyLinks.length > 0 && privacyLinks[0].confidence < 0.7) {
      recommendations.push({
        priority: 3,
        title: 'Improve privacy policy link labeling',
        description: 'Use clear, standard terminology for privacy policy links',
        implementation: 'Label links as "Privacy Policy" or "Data Protection" instead of generic terms',
        effort: 'minimal' as const,
        impact: 'medium' as const,
      });
    }

    return recommendations;
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    await this.checkTemplate.cleanup();
  }
}
